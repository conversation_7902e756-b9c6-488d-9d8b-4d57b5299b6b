import { sendMessageToAI } from './geminiService';
import type { Message } from '../types';
import type { ContextFile } from '../src/services/contextFileService';

export interface QuickPromptResult {
  optimizedPrompt: string;
  metaAnalysis: string;
  recommendations: string[];
  exportFormats: {
    txt: string;
    json: string;
    md: string;
  };
}

class QuickPromptGeneratorService {
  /**
   * Génère rapidement un prompt optimisé basé sur la description du problème
   */
  async generateQuickPrompt(problemDescription: string, contextFiles?: ContextFile[]): Promise<QuickPromptResult> {
    const systemPrompt = this.createQuickPromptSystemPrompt();
    
    // Préparer les messages pour l'API
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: problemDescription }
    ];

    // Utiliser le service Gemini pour générer le prompt
    const response = await sendMessageToAI(messages, 'génération');

    return this.parseQuickPromptResponse(response.content, problemDescription);
  }

  /**
   * C<PERSON>e le prompt système optimisé pour la génération rapide
   */
  private createQuickPromptSystemPrompt(): string {
    return `
INSTRUCTION CRITIQUE: RÉPONDEZ UNIQUEMENT EN FRANÇAIS. Aucun mot ou expression en anglais n'est autorisé.

Vous êtes ROONY, expert en ingénierie de prompts et en résolution de problèmes complexes. Votre mission est de générer RAPIDEMENT un prompt optimisé basé sur la description du problème de l'utilisateur.

VOTRE TÂCHE:
Analysez le problème décrit par l'utilisateur et créez immédiatement un prompt optimisé qu'il pourra utiliser dans n'importe quel LLM (ChatGPT, Claude, Gemini, etc.).

STRUCTURE OBLIGATOIRE DE VOTRE RÉPONSE:

### 🎯 Le Prompt Optimisé

[Ici, rédigez un prompt complet, autonome et directement utilisable. Le prompt doit être précis, structuré et inclure:]
- Le contexte du problème
- Les objectifs spécifiques
- Les contraintes importantes
- Le format de réponse souhaité
- Les techniques d'ingénierie de prompts appropriées (Chain-of-Thought, Role-Playing, etc.)

---

### 🔍 Méta-Analyse de la Construction

[Expliquez en 3-4 paragraphes:]
- Pourquoi vous avez structuré le prompt ainsi
- Quelles techniques de prompting vous avez utilisées
- Les points clés qui rendent ce prompt efficace
- Les adaptations possibles selon le LLM utilisé

---

### 💡 Recommandations Stratégiques

[Listez 4-5 recommandations concrètes pour:]
- Optimiser l'utilisation du prompt
- Itérer sur les résultats
- Adapter le prompt selon les retours
- Maximiser l'efficacité de l'IA

RÈGLES IMPORTANTES:
- Le prompt généré doit être AUTONOME (utilisable sans contexte supplémentaire)
- Utilisez des techniques de prompting avancées appropriées au problème
- Incluez des instructions claires pour l'IA cible
- Optimisez pour la clarté et l'efficacité
- Restez concis mais complet

RAPPEL: Votre réponse complète doit être en français uniquement.
`;
  }

  /**
   * Parse la réponse et structure les données
   */
  private parseQuickPromptResponse(response: string, originalProblem: string): QuickPromptResult {
    const sections = response.split('---');
    
    const optimizedPrompt = sections[0]
      ?.replace(/^### 🎯 Le Prompt Optimisé/i, '')
      ?.trim() || '';
    
    const metaAnalysis = sections[1]
      ?.replace(/^### 🔍 Méta-Analyse de la Construction/i, '')
      ?.trim() || '';
    
    const recommendationsSection = sections[2] || '';
    const recommendations = this.extractRecommendations(recommendationsSection);

    // Générer les formats d'export
    const exportFormats = this.generateExportFormats(optimizedPrompt, metaAnalysis, originalProblem);

    return {
      optimizedPrompt,
      metaAnalysis,
      recommendations,
      exportFormats
    };
  }

  /**
   * Extrait les recommandations de la section correspondante
   */
  private extractRecommendations(section: string): string[] {
    const recommendations: string[] = [];
    const lines = section.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('-') || trimmedLine.startsWith('•') || trimmedLine.match(/^\d+\./)) {
        recommendations.push(trimmedLine.replace(/^[-•\d.]\s*/, ''));
      }
    }
    
    return recommendations.length > 0 ? recommendations : [
      'Testez le prompt avec différents exemples',
      'Ajustez les instructions selon vos besoins spécifiques',
      'Itérez sur les résultats pour améliorer la précision',
      'Documentez les performances pour optimisations futures',
      'Adaptez le ton et le style selon votre contexte'
    ];
  }

  /**
   * Génère les différents formats d'export
   */
  private generateExportFormats(prompt: string, metaAnalysis: string, originalProblem: string) {
    const timestamp = new Date().toLocaleString('fr-FR');
    
    // Format TXT
    const txt = `PROMPT OPTIMISÉ GÉNÉRÉ PAR ROONY
=====================================

Problème original: ${originalProblem}
Généré le: ${timestamp}

LE PROMPT OPTIMISÉ:
${prompt}

MÉTA-ANALYSE:
${metaAnalysis}

---
Généré par Roony - Studio Agentique FlexoDiv
`;

    // Format JSON
    const json = JSON.stringify({
      metadata: {
        generatedBy: 'Roony - Studio Agentique',
        timestamp: timestamp,
        originalProblem: originalProblem,
        version: '3.0'
      },
      optimizedPrompt: prompt,
      metaAnalysis: metaAnalysis,
      usage: {
        instructions: 'Copiez le prompt optimisé et utilisez-le dans votre LLM préféré',
        compatibleWith: ['ChatGPT', 'Claude', 'Gemini', 'DeepSeek', 'Qwen', 'Mistral']
      }
    }, null, 2);

    // Format Markdown
    const md = `# Prompt Optimisé - Génération Rapide

**Problème original:** ${originalProblem}  
**Généré le:** ${timestamp}  
**Par:** Roony - Studio Agentique FlexoDiv

## 🎯 Le Prompt Optimisé

${prompt}

---

## 🔍 Méta-Analyse de la Construction

${metaAnalysis}

---

## 🚀 Utilisation

1. **Copiez** le prompt optimisé ci-dessus
2. **Collez-le** dans votre LLM préféré (ChatGPT, Claude, Gemini, etc.)
3. **Adaptez** si nécessaire selon vos besoins spécifiques
4. **Testez** avec vos données réelles
5. **Itérez** pour améliorer les résultats

## 📞 Support

**Développé par:** FlexoDiv - Studio Agentique  
**Version:** Roony 3.0  
**Contact:** <EMAIL>

---

*Généré automatiquement par Roony Agent le ${timestamp}*
`;

    return { txt, json, md };
  }
}

export const quickPromptGeneratorService = new QuickPromptGeneratorService();
