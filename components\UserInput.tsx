import React, { useState, useRef, useEffect } from 'react';
import { ContextFileUploader } from './ContextFileUploader';
import { contextFileService, type ContextFile } from '../src/services/contextFileService';

interface UserInputProps {
  isProcessing: boolean;
  onSendMessage: (message: string, contextFiles?: ContextFile[]) => void;
  isWorkflowStarted: boolean;
  isWorkflowComplete: boolean;
  lastAiMessage?: string; // Nouveau prop pour détecter les messages "Continuer"
}

export const UserInput: React.FC<UserInputProps> = ({ isProcessing, onSendMessage, isWorkflowStarted, isWorkflowComplete, lastAiMessage }) => {
  const [text, setText] = useState('');
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const [showContinueHelper, setShowContinueHelper] = useState(false);
  const [contextFiles, setContextFiles] = useState<ContextFile[]>([]);
  const [showFileUploader, setShowFileUploader] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim() && !isProcessing) {
      // Inclure les fichiers contextuels dans l'envoi
      onSendMessage(text, contextFiles.length > 0 ? contextFiles : undefined);
      setText('');
    }
  };

  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto';
      textAreaRef.current.style.height = `${textAreaRef.current.scrollHeight}px`;
    }
  }, [text]);

  // Détecter si l'IA demande de "Continuer" et proposer une aide
  useEffect(() => {
    if (lastAiMessage && lastAiMessage.toLowerCase().includes('continuer')) {
      setShowContinueHelper(true);
      // Masquer l'aide après 10 secondes
      const timer = setTimeout(() => setShowContinueHelper(false), 10000);
      return () => clearTimeout(timer);
    }
  }, [lastAiMessage]);

  const handleContinueClick = () => {
    setText('Continuer');
    setShowContinueHelper(false);
    // Focus sur le textarea pour que l'utilisateur puisse envoyer immédiatement
    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  };

  const getButtonText = () => {
    if (isWorkflowComplete) return 'Terminé';
    if (!isWorkflowStarted) return 'Lancer l\'Analyse';
    return 'Envoyer';
  };
  
  const getPlaceholderText = () => {
  if (isWorkflowComplete && isProcessing) return 'Veuillez vous authentifier pour commencer...';
  if (isWorkflowComplete) return 'Toutes les étapes sont terminées !';
  if (!isWorkflowStarted) return 'Décrivez votre problème pour commencer.\nVous pouvez ajouter des fichiers pour enrichir votre analyse';
  return 'Votre réponse... Ajoutez des documents si nécessaire pour plus de contexte';
  };

  const handleFilesChange = (files: ContextFile[]) => {
    setContextFiles(files);
  };

  const toggleFileUploader = () => {
    setShowFileUploader(!showFileUploader);
  };

  return (
    <div className="space-y-2">
      {/* Aide contextuelle pour "Continuer" */}
      {showContinueHelper && (
        <div className="bg-amber-900/50 border border-amber-600/50 rounded-lg p-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-amber-200">L'IA attend que vous écriviez "Continuer" pour passer à l'étape suivante</span>
          </div>
          <button
            onClick={handleContinueClick}
            className="bg-amber-600 hover:bg-amber-500 text-white text-sm font-semibold py-1 px-3 rounded transition-colors"
          >
            Continuer
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Zone principale du textarea */}
        <div className="w-full">
          <textarea
            ref={textAreaRef}
            value={text}
            onChange={(e) => setText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
            placeholder={getPlaceholderText()}
            className="w-full bg-slate-700 rounded-lg p-3 text-slate-200 placeholder-slate-400 resize-none focus:ring-2 focus:ring-indigo-500 focus:outline-none transition-shadow duration-200 max-h-48"
            rows={1}
            disabled={isProcessing || isWorkflowComplete}
          />
          
          {/* Indicateur de fichiers contextuels */}
          {contextFiles.length > 0 && (
            <div className="flex items-center gap-2 mt-2 px-2">
              <span className="text-xs text-emerald-400 flex items-center gap-1">
                📎 {contextFiles.length} fichier(s) ajouté(s)
              </span>
              <button
                onClick={() => {
                  contextFileService.clearAllContextFiles();
                  setContextFiles([]);
                }}
                className="text-xs text-red-400 hover:text-red-300 transition-colors"
              >
                Retirer tout
              </button>
            </div>
          )}
        </div>
        
        {/* Zone de téléchargement de fichiers contextuels */}
        {showFileUploader && (
          <div className="bg-slate-800/50 border border-slate-600 rounded-lg p-4 mb-3">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-slate-200">📎 Fichiers Contextuels</h3>
              <button
                onClick={toggleFileUploader}
                className="text-slate-400 hover:text-slate-200 transition-colors"
              >
                ✕
              </button>
            </div>
            <ContextFileUploader onFilesChange={handleFilesChange} />
            
            {/* Message de sécurité */}
            <div className="mt-3 p-2 bg-green-900/20 border border-green-500/30 rounded-md">
              <div className="flex items-start gap-2">
                <svg className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <div className="text-xs text-green-300">
                  <div className="font-medium mb-1">🔒 Sécurité garantie</div>
                  <div>Aucun fichier n'est sauvegardé sur nos serveurs. Tout est supprimé automatiquement à la fin de votre analyse.</div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Ligne des boutons - Parfaitement alignée avec flexbox */}
        <div className="flex items-center justify-between gap-3">
          {/* Section gauche - Gestion des fichiers contextuels */}
          <div className="flex items-center gap-3">
            {!showFileUploader && (
              <button
                onClick={toggleFileUploader}
                className="text-sm text-slate-200 hover:text-white transition-colors bg-slate-700/70 hover:bg-slate-600/70 px-4 py-2 rounded-md border border-slate-600 hover:border-slate-500 flex items-center gap-2 font-medium"
                title="Joindre des fichiers pour enrichir votre analyse"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
                <strong>Joindre Fichiers</strong>
              </button>
            )}
          </div>
          
          {/* Section droite - Bouton principal */}
          <button
            type="submit"
            disabled={isProcessing || !text.trim() || isWorkflowComplete}
            className="bg-indigo-600 text-white font-semibold py-3 px-6 rounded-lg hover:bg-indigo-500 disabled:bg-slate-600 disabled:cursor-not-allowed transition-colors duration-200 flex-shrink-0"
          >
            {isProcessing ? '...' : <strong>{getButtonText()}</strong>}
          </button>
        </div>
      </form>
    </div>
  );
};