import React, { useState } from 'react';
import { quickPromptGeneratorService, type QuickPromptResult } from '../services/quickPromptGeneratorService';
import type { ContextFile } from '../src/services/contextFileService';
import { ContextFileUploader } from './ContextFileUploader';
import { exportToPDF } from '../services/reportService';
import { dataDocsIntegrationService } from '../src/services/dataDocsIntegrationService';
import RoonyInlineAnimation from './RoonyInlineAnimation';

interface QuickPromptGeneratorProps {
  onBackToSelection: () => void;
}

const CopyIcon: React.FC = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
  </svg>
);

// URLs des LLMs populaires pour les tests
const LLM_URLS = {
  'ChatGPT (GPT-4, GPT-3.5)': 'https://chat.openai.com',
  'Claude (Anthropic)': 'https://claude.ai',
  'Gemini (Google)': 'https://gemini.google.com',
  'Perplexity AI': 'https://www.perplexity.ai',
  'DeepSeek': 'https://chat.deepseek.com',
  'Qwen': 'https://tongyi.aliyun.com',
  'Mistral AI': 'https://chat.mistral.ai',
  'Llama (Meta)': 'https://www.meta.ai'
};

export const QuickPromptGenerator: React.FC<QuickPromptGeneratorProps> = ({ onBackToSelection }) => {
  const [problemDescription, setProblemDescription] = useState('');
  const [contextFiles, setContextFiles] = useState<ContextFile[]>([]);
  const [showFileUploader, setShowFileUploader] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<QuickPromptResult | null>(null);
  const [copyButtonText, setCopyButtonText] = useState('Copier');
  const [showCelebration, setShowCelebration] = useState(false);

  const handleGenerate = async () => {
    if (!problemDescription.trim()) return;

    setIsGenerating(true);
    try {
      const quickResult = await quickPromptGeneratorService.generateQuickPrompt(
        problemDescription,
        contextFiles.length > 0 ? contextFiles : undefined
      );
      setResult(quickResult);
      setShowCelebration(true);
      setTimeout(() => setShowCelebration(false), 3000);
    } catch (error) {
      console.error('Erreur lors de la génération rapide:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopyButtonText('Copié !');
      setTimeout(() => setCopyButtonText('Copier'), 2000);
    });
  };

  // Nouvelles fonctions pour les boutons d'action
  const handleIterate = () => {
    // Remettre le prompt dans la zone de saisie pour itération
    setProblemDescription(result?.optimizedPrompt || '');
    setResult(null);
    setShowCelebration(false);
  };

  const handleDeploy = () => {
    // Copier le prompt et afficher des instructions de déploiement
    if (result?.optimizedPrompt) {
      navigator.clipboard.writeText(result.optimizedPrompt);
      alert('✅ Prompt copié dans le presse-papiers !\n\n🚀 Instructions de déploiement :\n1. Collez le prompt dans votre application\n2. Testez avec vos données réelles\n3. Ajustez si nécessaire\n4. Intégrez dans votre workflow');
    }
  };

  const handleLearnMore = () => {
    // Rediriger vers l'analyse complète
    if (onBackToSelection) {
      onBackToSelection(); // Retour à la sélection pour choisir l'analyse complète
    }
  };

  const handleExport = (format: 'txt' | 'json' | 'md' | 'pdf') => {
    if (!result) return;

    let content = '';
    let mimeType = '';
    let filename = `prompt-rapide-${new Date().toISOString().split('T')[0]}.${format}`;

    switch (format) {
      case 'txt':
        content = result.exportFormats.txt;
        mimeType = 'text/plain';
        break;
      case 'json':
        content = result.exportFormats.json;
        mimeType = 'application/json';
        break;
      case 'md':
        content = result.exportFormats.md;
        mimeType = 'text/markdown';
        break;
      case 'pdf':
        // Pour le PDF, on utilise l'API de génération PDF existante
        try {
          exportToPDF(result.exportFormats.md, filename.replace('.pdf', ''));
          return; // Sortir de la fonction après l'export PDF réussi
        } catch (error) {
          console.error('Erreur lors de l\'export PDF:', error);
          // Fallback vers le téléchargement Markdown
          content = result.exportFormats.md;
          mimeType = 'text/markdown';
          filename = filename.replace('.pdf', '.md');
          // Continuer avec le téléchargement Markdown
        }
        break;
    }

    const blob = new Blob([content], { type: mimeType, endings: 'native' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleFilesChange = (files: ContextFile[]) => {
    setContextFiles(files);
  };

  if (result) {
    return (
      <div className="flex-grow flex flex-col p-6 overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
            🚀 Prompt Généré Rapidement
          </h2>

          {/* Animation de célébration */}
          <RoonyInlineAnimation
            trigger={showCelebration}
            animation="proud"
            size={100}
            duration={3000}
            className="flex-shrink-0"
            onComplete={() => setShowCelebration(false)}
          />
        </div>

        {/* Prompt Optimisé */}
        <div className="bg-slate-900/70 p-4 rounded-xl border border-slate-700 mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-blue-300">🎯 Prompt Optimisé</h3>
            <button 
              onClick={() => handleCopy(result.optimizedPrompt)}
              className="bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-1 px-3 rounded-md flex items-center transition-colors"
            >
              <CopyIcon /> {copyButtonText}
            </button>
          </div>
          <div className="text-slate-300 whitespace-pre-wrap font-mono text-sm p-3 bg-black/20 rounded-md">
            {result.optimizedPrompt}
          </div>
        </div>

        {/* Méta-Analyse */}
        <div className="bg-slate-900/70 p-4 rounded-xl border border-slate-700 mb-6">
          <h3 className="text-lg font-semibold text-purple-300 mb-3">🔍 Méta-Analyse</h3>
          <div className="text-slate-300 whitespace-pre-wrap text-sm">
            {result.metaAnalysis}
          </div>
        </div>

        {/* Recommandations */}
        {result.recommendations.length > 0 && (
          <div className="bg-slate-900/70 p-4 rounded-xl border border-slate-700 mb-6">
            <h3 className="text-lg font-semibold text-green-300 mb-3">💡 Recommandations</h3>
            <div className="space-y-2">
              {result.recommendations.map((rec, index) => (
                <div key={index} className="flex items-start gap-2">
                  <span className="text-green-400 mt-1">•</span>
                  <span className="text-slate-300 text-sm">{rec}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Options d'export */}
        <div className="bg-gradient-to-r from-indigo-900/30 to-purple-900/30 p-6 rounded-xl border border-indigo-500/30">
          <h4 className="text-md font-semibold mb-4 text-indigo-300">💾 Exporter votre prompt</h4>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
            <button
              onClick={() => handleExport('txt')}
              className="bg-slate-600/80 hover:bg-slate-600 text-white text-sm font-semibold py-2 px-4 rounded transition-colors"
            >
              📄 TXT
            </button>
            <button
              onClick={() => handleExport('json')}
              className="bg-slate-600/80 hover:bg-slate-600 text-white text-sm font-semibold py-2 px-4 rounded transition-colors"
            >
              📋 JSON
            </button>
            <button
              onClick={() => handleExport('md')}
              className="bg-slate-600/80 hover:bg-slate-600 text-white text-sm font-semibold py-2 px-4 rounded transition-colors"
            >
              📝 Markdown
            </button>
            <button
              onClick={() => handleExport('pdf')}
              className="bg-emerald-600 hover:bg-emerald-500 text-white text-sm font-semibold py-2 px-4 rounded transition-colors"
            >
              📑 PDF
            </button>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-gradient-to-r from-slate-900/30 to-slate-800/30 p-6 rounded-xl border border-slate-600/30 mt-6">
          <h4 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400 mb-4 text-center">
            🎯 Et maintenant, que souhaitez-vous faire ?
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={onBackToSelection}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Nouveau Prompt
            </button>

            <button
              onClick={handleIterate}
              className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-500 hover:to-red-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Affiner ce Prompt
            </button>

            {/* Section de test avec plusieurs LLMs */}
            <div className="bg-slate-800/50 p-4 rounded-lg border border-slate-600">
              <h4 className="text-lg font-semibold text-green-300 mb-3 text-center">🧪 Tester votre prompt</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {Object.entries(LLM_URLS).map(([name, url]) => (
                  <button
                    key={name}
                    onClick={() => window.open(url, '_blank')}
                    className="bg-gradient-to-r from-green-600/80 to-emerald-600/80 hover:from-green-500 hover:to-emerald-500 text-white text-xs font-medium py-2 px-3 rounded-md transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-1"
                    title={`Tester dans ${name}`}
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    {name.split(' ')[0]}
                  </button>
                ))}
              </div>
              <p className="text-xs text-slate-400 text-center mt-2">
                Cliquez sur un LLM pour ouvrir son interface et tester votre prompt
              </p>
            </div>
          </div>

          <div className="text-center mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
              <button
                onClick={handleIterate}
                className="bg-slate-800/50 hover:bg-slate-700/50 p-3 rounded-lg border border-slate-600 hover:border-blue-400/50 transition-all duration-200 transform hover:scale-105 cursor-pointer"
              >
                <div className="text-blue-400 font-semibold mb-1">🔄 Itérer</div>
                <div className="text-slate-300">Testez et revenez pour améliorer</div>
              </button>
              <button
                onClick={handleDeploy}
                className="bg-slate-800/50 hover:bg-slate-700/50 p-3 rounded-lg border border-slate-600 hover:border-green-400/50 transition-all duration-200 transform hover:scale-105 cursor-pointer"
              >
                <div className="text-green-400 font-semibold mb-1">🚀 Déployer</div>
                <div className="text-slate-300">Utilisez dans vos projets IA</div>
              </button>
              <button
                onClick={handleLearnMore}
                className="bg-slate-800/50 hover:bg-slate-700/50 p-3 rounded-lg border border-slate-600 hover:border-purple-400/50 transition-all duration-200 transform hover:scale-105 cursor-pointer"
              >
                <div className="text-purple-400 font-semibold mb-1">📚 Apprendre</div>
                <div className="text-slate-300">Essayez l'analyse complète</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-grow flex flex-col p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">
            🚀 Génération Rapide de Prompt
          </h2>
          <p className="text-slate-400 mt-2">
            Décrivez votre problème et obtenez un prompt optimisé en quelques secondes
          </p>
        </div>
        
        <button
          onClick={onBackToSelection}
          className="bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Retour
        </button>
      </div>

      <div className="space-y-6">
        {/* Zone de saisie du problème */}
        <div className="bg-slate-900/70 p-6 rounded-xl border border-slate-700">
          <h3 className="text-lg font-semibold text-blue-300 mb-4">📝 Décrivez votre problème</h3>
          
          <textarea
            value={problemDescription}
            onChange={(e) => setProblemDescription(e.target.value)}
            placeholder="Exemple: Je veux créer un chatbot pour mon site e-commerce qui aide les clients à choisir le bon produit selon leurs besoins et leur budget..."
            className="w-full p-4 bg-slate-800/50 border border-slate-600 rounded-lg text-slate-200 placeholder-slate-400 resize-none focus:border-blue-500 focus:outline-none"
            rows={6}
          />
          
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setShowFileUploader(!showFileUploader)}
                className="bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
                Ajouter des fichiers
                {contextFiles.length > 0 && (
                  <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                    {contextFiles.length}
                  </span>
                )}
              </button>
              
              <span className="text-sm text-slate-400">
                {problemDescription.length}/1000 caractères recommandés
              </span>
            </div>
            
            <button
              onClick={handleGenerate}
              disabled={!problemDescription.trim() || isGenerating}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 disabled:from-slate-600 disabled:to-slate-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center gap-2"
            >
              {isGenerating ? (
                <>
                  <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Génération...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Générer le Prompt
                </>
              )}
            </button>
          </div>
        </div>

        {/* Uploader de fichiers */}
        {showFileUploader && (
          <div className="bg-slate-900/70 p-6 rounded-xl border border-slate-700">
            <h3 className="text-lg font-semibold text-purple-300 mb-4">📎 Fichiers Contextuels</h3>
            <ContextFileUploader onFilesChange={handleFilesChange} />
          </div>
        )}

        {/* Conseils */}
        <div className="bg-gradient-to-r from-emerald-900/30 to-teal-900/30 p-6 rounded-xl border border-emerald-500/30">
          <h3 className="text-lg font-semibold text-emerald-300 mb-4">💡 Conseils pour un meilleur prompt</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-emerald-400 mb-2">✅ À faire :</h4>
              <ul className="space-y-1 text-slate-300">
                <li>• Soyez spécifique sur vos objectifs</li>
                <li>• Mentionnez le contexte d'utilisation</li>
                <li>• Précisez les contraintes importantes</li>
                <li>• Indiquez le format de réponse souhaité</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-red-400 mb-2">❌ À éviter :</h4>
              <ul className="space-y-1 text-slate-300">
                <li>• Descriptions trop vagues</li>
                <li>• Objectifs multiples non hiérarchisés</li>
                <li>• Oublier le public cible</li>
                <li>• Négliger les contraintes techniques</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
